# LinkedIn AI Message Helper

A Chrome extension that automatically generates personalized messages for LinkedIn company pages using AI.

## Features

- **Automatic Company Information Extraction**: Extracts overview and company details from LinkedIn company pages
- **Template Message Generation**: Generates professional, personalized outreach messages using company information
- **Automatic Message Population**: Detects when you open a message compose box and auto-fills it with the generated message
- **Company Page Integration**: Works directly on LinkedIn company pages when you click the "Message" button
- **Professional Templates**: Uses well-crafted message templates that can be customized

## Installation

1. Clone or download this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension icon should appear in your toolbar

## Usage

1. **Configure the Extension** (Optional):

   - Click the extension icon in your toolbar
   - Customize settings if needed (extension works with default settings)

2. **Generate Messages**:
   - Visit any LinkedIn company page (e.g., `https://www.linkedin.com/company/microsoft/`)
   - The extension will automatically extract company information
   - Click the "Message" button on the company page
   - A "New message" popup will appear with "Write a message..." placeholder
   - The extension will automatically populate the message box with a personalized message

## Configuration

### System Prompt

The system prompt tells the AI how to generate messages. The default prompt creates professional, concise outreach messages. You can customize it for your specific use case (sales, recruiting, partnerships, etc.).

### OpenAI API Key

- **With API Key**: Real AI-generated messages using GPT-3.5-turbo
- **Without API Key**: Demo mode with randomized template messages

## File Structure

- `manifest.json` - Extension configuration
- `background.js` - Background service worker for tab monitoring and AI integration
- `content.js` - Content script for page interaction and message population
- `utils.js` - Utility functions for DOM manipulation and text processing
- `popup.html/css/js` - Extension popup interface for configuration

## Technical Details

### Permissions

- `activeTab` - Access to current tab for content injection
- `tabs` - Monitor tab changes to detect LinkedIn company pages
- `storage` - Save user configuration (system prompt, API key)
- `host_permissions` - Access to LinkedIn domains

### Content Script Injection

The extension injects content scripts on:

- `https://www.linkedin.com/company/*` - Company pages for information extraction
- `https://www.linkedin.com/messaging/*` - Messaging pages for message population

### Message Box Detection

The extension monitors for various LinkedIn message compose box selectors:

- `.msg-form__contenteditable`
- `.compose-form__message-field`
- `.msg-form__message-texteditor`
- And several fallback selectors

## Troubleshooting

### Extension Not Working

1. Check that the extension is enabled in `chrome://extensions/`
2. Refresh the LinkedIn page after installing
3. Check the browser console for error messages

### Message Not Populating

1. Ensure you've visited a company page first to extract information
2. Try clicking in the message box to focus it
3. Check if the message was copied to clipboard as fallback

### API Issues

1. Verify your OpenAI API key is correct
2. Check your API usage limits
3. The extension will fall back to demo mode if API fails

## Development

To modify the extension:

1. Make changes to the source files
2. Go to `chrome://extensions/`
3. Click the refresh icon on the extension card
4. Test your changes

## Privacy & Security

- Company information is processed locally and sent only to OpenAI (if API key provided)
- API keys are stored locally in Chrome's sync storage
- No data is sent to external servers except OpenAI API calls
- All processing happens in the browser

## License

This project is for educational and personal use. Please respect LinkedIn's Terms of Service when using this extension.
