// Background script for LinkedIn AI Message Helper
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    // Check if the page has finished loading and is a LinkedIn company page
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes("https://www.linkedin.com/company/")) {
        console.log('LinkedIn company page detected:', tab.url);

        // Send message to content script to start processing
        chrome.tabs.sendMessage(tabId, {
            action: "companyPageLoaded",
            url: tab.url
        }).catch(() => {
            console.log('Content script not ready yet, will retry');
        });
    }
});

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.action === "generateMessage") {
        handleMessageGeneration(request, sendResponse);
        return true; // Keep the message channel open for async response
    }
});

async function handleMessageGeneration(request, sendResponse) {
    try {
        console.log('Generating message for company:', request.companyName);

        // Get stored system prompt and API key (optional for template mode)
        const storage = await chrome.storage.sync.get(['systemPrompt', 'openaiApiKey']);

        // For template mode, we don't require a system prompt
        // The template message will be generated regardless

        // Generate message using template (or OpenAI if configured)
        const generatedMessage = await generateAIMessage(
            storage.systemPrompt || 'default',
            request.companyInfo,
            storage.openaiApiKey
        );

        sendResponse({
            success: true,
            message: generatedMessage
        });

    } catch (error) {
        console.error('Error generating message:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

async function generateAIMessage(_systemPrompt, companyInfo, _apiKey) {
    // For now, always use the generic template message for easy testing
    // TODO: Uncomment the OpenAI integration when ready

    /*
    if (apiKey && apiKey.startsWith('sk-')) {
        // Use actual OpenAI API call
        return await callOpenAI(systemPrompt, companyInfo, apiKey);
    } else {
        // Demo mode - generate a template message
        return generateDemoMessage(companyInfo);
    }
    */

    // Always use template message for now
    return generateDemoMessage(companyInfo);
}

async function callOpenAI(systemPrompt, companyInfo, apiKey) {
    // Placeholder for actual OpenAI API implementation
    try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user',
                        content: `Based on this company information, generate a personalized outreach message:\n\n${companyInfo}`
                    }
                ],
                max_tokens: 200,
                temperature: 0.7
            })
        });

        const data = await response.json();

        if (data.choices && data.choices[0]) {
            return data.choices[0].message.content.trim();
        } else {
            throw new Error('Invalid response from OpenAI API');
        }
    } catch (error) {
        console.error('OpenAI API error:', error);
        // Fallback to demo message
        return generateDemoMessage(companyInfo);
    }
}

function generateDemoMessage(companyInfo) {
    // Extract company name from the info
    const lines = companyInfo.split('\n');
    let companyName = 'your company';

    // Try to extract company name from the first line
    const firstLine = lines[0] || '';
    if (firstLine.includes('Company:')) {
        companyName = firstLine.replace('Company:', '').trim();
    } else if (firstLine.trim()) {
        companyName = firstLine.trim();
    }

    // Generic professional message template
    const genericMessage = `Hi there!

I hope this message finds you well. I came across ${companyName} and was impressed by your work in the industry.

I'd love to connect and explore potential opportunities for collaboration. Would you be open to a brief conversation about how we might work together?

Looking forward to hearing from you.

Best regards`;

    return genericMessage;
}