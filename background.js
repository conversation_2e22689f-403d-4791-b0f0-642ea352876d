// Background script for LinkedIn AI Message Helper
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    // Check if the page has finished loading and is a LinkedIn company page
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes("https://www.linkedin.com/company/")) {
        console.log('LinkedIn company page detected:', tab.url);

        // Send message to content script to start processing
        chrome.tabs.sendMessage(tabId, {
            action: "companyPageLoaded",
            url: tab.url
        }).catch(() => {
            console.log('Content script not ready yet, will retry');
        });
    }
});

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.action === "generateMessage") {
        handleMessageGeneration(request, sendResponse);
        return true; // Keep the message channel open for async response
    }
});

async function handleMessageGeneration(request, sendResponse) {
    try {
        console.log('Generating message for company:', request.companyName);

        // Get stored system prompt and API key (optional for template mode)
        const storage = await chrome.storage.sync.get(['systemPrompt', 'openaiApiKey']);

        // For template mode, we don't require a system prompt
        // The template message will be generated regardless

        // Generate message using template (or OpenAI if configured)
        const generatedMessage = await generateAIMessage(
            storage.systemPrompt || 'default',
            request.companyInfo,
            storage.openaiApiKey
        );

        sendResponse({
            success: true,
            message: generatedMessage
        });

    } catch (error) {
        console.error('Error generating message:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

async function generateAIMessage(systemPrompt, companyInfo, apiKey) {
    // Use OpenAI if API key is provided, otherwise use template
    if (apiKey && apiKey.startsWith('sk-')) {
        console.log('Using OpenAI API for message generation');
        return await callOpenAI(systemPrompt, companyInfo, apiKey);
    } else {
        console.log('Using template message (no API key provided)');
        return generateDemoMessage(companyInfo);
    }
}

async function callOpenAI(systemPrompt, companyInfo, apiKey) {
    try {
        console.log('Making OpenAI API call...');

        const defaultSystemPrompt = `You are a professional business development representative. Generate a personalized LinkedIn outreach message based on the company information provided. The message should be:
- Professional and respectful
- Personalized based on company details
- Brief (2-3 sentences maximum)
- Include a clear but soft call to action
- Avoid being overly salesy
- Sound natural and human`;

        const actualSystemPrompt = systemPrompt && systemPrompt !== 'default' ? systemPrompt : defaultSystemPrompt;

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: actualSystemPrompt
                    },
                    {
                        role: 'user',
                        content: `Generate a personalized LinkedIn outreach message based on this company information:\n\n${companyInfo}`
                    }
                ],
                max_tokens: 150,
                temperature: 0.7
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
        }

        const data = await response.json();

        if (data.choices && data.choices[0] && data.choices[0].message) {
            const generatedMessage = data.choices[0].message.content.trim();
            console.log('OpenAI message generated successfully');
            return generatedMessage;
        } else {
            throw new Error('Invalid response structure from OpenAI API');
        }
    } catch (error) {
        console.error('OpenAI API error:', error);
        console.log('Falling back to template message');
        // Fallback to demo message
        return generateDemoMessage(companyInfo);
    }
}

function generateDemoMessage(companyInfo) {
    // Extract company name from the info
    const lines = companyInfo.split('\n');
    let companyName = 'your company';

    // Try to extract company name from the first line
    const firstLine = lines[0] || '';
    if (firstLine.includes('Company:')) {
        companyName = firstLine.replace('Company:', '').trim();
    } else if (firstLine.trim()) {
        companyName = firstLine.trim();
    }

    // Generic professional message template
    const genericMessage = `Hi there!

I hope this message finds you well. I came across ${companyName} and was impressed by your work in the industry.

I'd love to connect and explore potential opportunities for collaboration. Would you be open to a brief conversation about how we might work together?

Looking forward to hearing from you.

Best regards`;

    return genericMessage;
}