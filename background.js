// Background script for LinkedIn AI Message Helper
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    // Check if the page has finished loading and is a LinkedIn company page
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes("https://www.linkedin.com/company/")) {
        console.log('LinkedIn company page detected:', tab.url);

        // Send message to content script to start processing
        chrome.tabs.sendMessage(tabId, {
            action: "companyPageLoaded",
            url: tab.url
        }).catch(() => {
            console.log('Content script not ready yet, will retry');
        });
    }
});

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.action === "generateMessage") {
        handleMessageGeneration(request, sendResponse);
        return true; // Keep the message channel open for async response
    }
});

async function handleMessageGeneration(request, sendResponse) {
    try {
        console.log('Generating message for company:', request.companyName);

        // Get stored system prompt and API key
        const storage = await chrome.storage.sync.get(['systemPrompt', 'openaiApiKey']);

        if (!storage.systemPrompt) {
            sendResponse({
                success: false,
                error: "No system prompt configured. Please set it in the extension popup."
            });
            return;
        }

        // Generate message using OpenAI (or placeholder)
        const generatedMessage = await generateAIMessage(
            storage.systemPrompt,
            request.companyInfo,
            storage.openaiApiKey
        );

        sendResponse({
            success: true,
            message: generatedMessage
        });

    } catch (error) {
        console.error('Error generating message:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

async function generateAIMessage(systemPrompt, companyInfo, apiKey) {
    // This is a placeholder function - replace with actual OpenAI API call
    if (apiKey && apiKey.startsWith('sk-')) {
        // TODO: Implement actual OpenAI API call
        return await callOpenAI(systemPrompt, companyInfo, apiKey);
    } else {
        // Demo mode - generate a placeholder message
        return generateDemoMessage(companyInfo);
    }
}

async function callOpenAI(systemPrompt, companyInfo, apiKey) {
    // Placeholder for actual OpenAI API implementation
    try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user',
                        content: `Based on this company information, generate a personalized outreach message:\n\n${companyInfo}`
                    }
                ],
                max_tokens: 200,
                temperature: 0.7
            })
        });

        const data = await response.json();

        if (data.choices && data.choices[0]) {
            return data.choices[0].message.content.trim();
        } else {
            throw new Error('Invalid response from OpenAI API');
        }
    } catch (error) {
        console.error('OpenAI API error:', error);
        // Fallback to demo message
        return generateDemoMessage(companyInfo);
    }
}

function generateDemoMessage(companyInfo) {
    // Extract company name from the info
    const lines = companyInfo.split('\n');
    const companyName = lines[0] || 'your company';

    const demoMessages = [
        `Hi! I noticed ${companyName}'s innovative approach and would love to discuss how we can support your growth objectives. Would you be open to a brief conversation?`,
        `Hello! I've been following ${companyName} and am impressed by your work. I believe we have solutions that could benefit your team. Could we schedule a quick call?`,
        `Hi there! ${companyName} caught my attention with your recent developments. I'd love to explore potential collaboration opportunities. Are you available for a brief discussion?`
    ];

    return demoMessages[Math.floor(Math.random() * demoMessages.length)];
}