// Utility functions for LinkedIn AI Message Helper

// Wait for element to appear in DOM
function waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
        const element = document.querySelector(selector);
        if (element) {
            resolve(element);
            return;
        }

        const observer = new MutationObserver((_mutations, obs) => {
            const element = document.querySelector(selector);
            if (element) {
                obs.disconnect();
                resolve(element);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        setTimeout(() => {
            observer.disconnect();
            reject(new Error(`Element ${selector} not found within ${timeout}ms`));
        }, timeout);
    });
}

// Wait for multiple elements
function waitForAnyElement(selectors, timeout = 10000) {
    return new Promise((resolve, reject) => {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                resolve({ element, selector });
                return;
            }
        }

        const observer = new MutationObserver((_mutations, obs) => {
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve({ element, selector });
                    return;
                }
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        setTimeout(() => {
            observer.disconnect();
            reject(new Error(`None of the elements found within ${timeout}ms`));
        }, timeout);
    });
}

// Extract text content safely
function extractTextContent(element) {
    if (!element) return '';

    // Remove script and style elements
    const clone = element.cloneNode(true);
    const scripts = clone.querySelectorAll('script, style');
    scripts.forEach(script => script.remove());

    return clone.textContent || clone.innerText || '';
}

// Clean and format text
function cleanText(text) {
    return text
        .replace(/\s+/g, ' ')  // Replace multiple whitespace with single space
        .replace(/\n+/g, '\n') // Replace multiple newlines with single newline
        .trim();
}

// Debounce function to limit rapid function calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Check if we're on a LinkedIn company page
function isLinkedInCompanyPage() {
    return window.location.href.includes('linkedin.com/company/');
}

// Check if we're on LinkedIn messaging
function isLinkedInMessaging() {
    return window.location.href.includes('linkedin.com/messaging/');
}

// Get company name from URL or page
function getCompanyNameFromPage() {
    // Try to get from URL first
    const urlMatch = window.location.href.match(/linkedin\.com\/company\/([^\/\?]+)/);
    if (urlMatch) {
        return urlMatch[1].replace(/-/g, ' ');
    }

    // Try to get from page title or heading
    const titleElement = document.querySelector('h1[data-anonymize="company-name"]') ||
                        document.querySelector('.org-top-card-summary__title') ||
                        document.querySelector('h1');

    if (titleElement) {
        return extractTextContent(titleElement);
    }

    return 'Unknown Company';
}

// Show notification to user
function showNotification(message, type = 'info', duration = 3000) {
    // Remove existing notifications
    const existing = document.querySelector('.linkedin-ai-notification');
    if (existing) {
        existing.remove();
    }

    const notification = document.createElement('div');
    notification.className = `linkedin-ai-notification linkedin-ai-${type}`;
    notification.textContent = message;

    // Styles
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '6px',
        color: 'white',
        fontFamily: 'Arial, sans-serif',
        fontSize: '14px',
        zIndex: '10000',
        maxWidth: '300px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
        transition: 'all 0.3s ease'
    });

    // Type-specific colors
    const colors = {
        info: '#0a66c2',
        success: '#4caf50',
        warning: '#ff9800',
        error: '#f44336'
    };

    notification.style.backgroundColor = colors[type] || colors.info;

    document.body.appendChild(notification);

    // Auto remove
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, duration);
}

// Log with timestamp
function log(message, ...args) {
    console.log(`[LinkedIn AI Helper ${new Date().toLocaleTimeString()}]`, message, ...args);
}

// Export functions for use in content script
if (typeof window !== 'undefined') {
    window.LinkedInAIUtils = {
        waitForElement,
        waitForAnyElement,
        extractTextContent,
        cleanText,
        debounce,
        isLinkedInCompanyPage,
        isLinkedInMessaging,
        getCompanyNameFromPage,
        showNotification,
        log
    };
}