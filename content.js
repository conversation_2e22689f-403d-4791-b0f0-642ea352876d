// Content script for LinkedIn AI Message Helper
(function() {
    'use strict';

    // Wait for utils to be available
    if (!window.LinkedInAIUtils) {
        console.error('LinkedInAIUtils not available');
        return;
    }

    const utils = window.LinkedInAIUtils;
    let companyInfo = null;
    let isProcessing = false;

    // Initialize the extension
    function init() {
        utils.log('LinkedIn AI Message Helper initialized');

        if (utils.isLinkedInCompanyPage()) {
            utils.log('Company page detected, extracting overview...');
            extractCompanyOverview();
        }

        // Set up message box monitoring
        setupMessageBoxMonitoring();
    }

    // Extract company overview information
    async function extractCompanyOverview() {
        try {
            utils.showNotification('Extracting company information...', 'info', 2000);

            const companyName = utils.getCompanyNameFromPage();
            utils.log('Company name:', companyName);

            // Wait for the overview section to load
            const overviewSelectors = [
                '[data-test-id="about-us-section"]',
                '.org-about-us-organization-description',
                '.org-top-card-summary__description',
                '.org-about-module__description',
                '.break-words p',
                '.org-about-us-organization-description__text'
            ];

            let overviewText = '';

            try {
                const result = await utils.waitForAnyElement(overviewSelectors, 5000);
                overviewText = utils.cleanText(utils.extractTextContent(result.element));
                utils.log('Found overview using selector:', result.selector);
            } catch (error) {
                utils.log('No overview section found, trying alternative extraction');
                overviewText = extractAlternativeCompanyInfo();
            }

            // Also try to get additional company info
            const additionalInfo = extractAdditionalCompanyInfo();

            companyInfo = {
                name: companyName,
                overview: overviewText,
                additional: additionalInfo,
                url: window.location.href
            };

            const fullInfo = formatCompanyInfo(companyInfo);
            utils.log('Extracted company info:', fullInfo);

            if (overviewText || additionalInfo) {
                utils.showNotification('Company information extracted successfully!', 'success');
            } else {
                utils.showNotification('Limited company information found', 'warning');
            }

        } catch (error) {
            utils.log('Error extracting company overview:', error);
            utils.showNotification('Error extracting company information', 'error');
        }
    }

    // Extract alternative company information if overview not found
    function extractAlternativeCompanyInfo() {
        const selectors = [
            '.org-top-card-summary__description',
            '.org-top-card-summary__info-item',
            '.org-page-details__definition-text',
            '.org-top-card-summary__tagline'
        ];

        let info = '';
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = utils.cleanText(utils.extractTextContent(element));
                if (text && text.length > 10) {
                    info += text + '\n';
                }
            }
        }

        return info.trim();
    }

    // Extract additional company information
    function extractAdditionalCompanyInfo() {
        const info = [];

        // Industry
        const industryElement = document.querySelector('.org-top-card-summary__industry') ||
                               document.querySelector('[data-test-id="industry"]');
        if (industryElement) {
            info.push(`Industry: ${utils.extractTextContent(industryElement)}`);
        }

        // Company size
        const sizeElement = document.querySelector('.org-top-card-summary__company-size') ||
                           document.querySelector('[data-test-id="company-size"]');
        if (sizeElement) {
            info.push(`Size: ${utils.extractTextContent(sizeElement)}`);
        }

        // Location
        const locationElement = document.querySelector('.org-top-card-summary__headquarter') ||
                               document.querySelector('[data-test-id="headquarters"]');
        if (locationElement) {
            info.push(`Location: ${utils.extractTextContent(locationElement)}`);
        }

        return info.join('\n');
    }

    // Format company information for AI processing
    function formatCompanyInfo(info) {
        let formatted = `Company: ${info.name}\n`;

        if (info.overview) {
            formatted += `\nOverview:\n${info.overview}\n`;
        }

        if (info.additional) {
            formatted += `\nAdditional Information:\n${info.additional}\n`;
        }

        return formatted.trim();
    }

    // Set up monitoring for message compose boxes
    function setupMessageBoxMonitoring() {
        // Monitor for message button clicks and compose box appearances
        const debouncedMonitor = utils.debounce(monitorForMessageBox, 500);

        // Watch for DOM changes that might indicate message box opening
        const observer = new MutationObserver(debouncedMonitor);
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Also check immediately
        monitorForMessageBox();
    }

    // Monitor for message compose boxes
    function monitorForMessageBox() {
        if (isProcessing) return;

        // Selectors for LinkedIn message compose boxes
        const messageBoxSelectors = [
            '.msg-form__contenteditable',
            '.msg-form__msg-content-container--scrollable .msg-form__placeholder',
            '.compose-form__message-field',
            '.msg-form__message-texteditor',
            '[data-test-id="compose-message-field"]',
            '.artdeco-text-input--input[placeholder*="message"]'
        ];

        for (const selector of messageBoxSelectors) {
            const messageBox = document.querySelector(selector);
            if (messageBox && !messageBox.hasAttribute('data-ai-helper-processed')) {
                utils.log('Message compose box detected:', selector);
                handleMessageBoxDetected(messageBox);
                break;
            }
        }
    }

    // Handle when message box is detected
    async function handleMessageBoxDetected(messageBox) {
        if (!companyInfo) {
            utils.showNotification('No company information available. Please visit a company page first.', 'warning');
            return;
        }

        try {
            isProcessing = true;
            messageBox.setAttribute('data-ai-helper-processed', 'true');

            utils.showNotification('Generating personalized message...', 'info');

            // Request message generation from background script
            const response = await chrome.runtime.sendMessage({
                action: 'generateMessage',
                companyInfo: formatCompanyInfo(companyInfo),
                companyName: companyInfo.name
            });

            if (response.success) {
                populateMessageBox(messageBox, response.message);
                utils.showNotification('Message generated successfully!', 'success');
            } else {
                utils.showNotification(`Error: ${response.error}`, 'error');
            }

        } catch (error) {
            utils.log('Error generating message:', error);
            utils.showNotification('Error generating message', 'error');
        } finally {
            isProcessing = false;
        }
    }

    // Populate the message box with generated content
    function populateMessageBox(messageBox, message) {
        try {
            // Add a small delay to ensure the message box is ready
            setTimeout(() => {
                // Clear existing content first
                if (messageBox.contentEditable === 'true') {
                    // For contenteditable elements (most LinkedIn message boxes)
                    messageBox.innerHTML = '';
                    messageBox.focus();

                    // Insert the message
                    const lines = message.split('\n');
                    lines.forEach((line, index) => {
                        if (index > 0) {
                            messageBox.appendChild(document.createElement('br'));
                        }
                        const textNode = document.createTextNode(line);
                        messageBox.appendChild(textNode);
                    });

                } else if (messageBox.tagName === 'TEXTAREA' || messageBox.tagName === 'INPUT') {
                    // For textarea/input elements
                    messageBox.value = message;
                } else {
                    // Fallback method
                    messageBox.textContent = message;
                }

                // Trigger events to notify LinkedIn's JavaScript
                const events = ['input', 'change', 'keyup', 'focus', 'blur'];
                events.forEach(eventType => {
                    try {
                        const event = new Event(eventType, { bubbles: true, cancelable: true });
                        messageBox.dispatchEvent(event);
                    } catch (e) {
                        // Some events might not be supported
                        utils.log(`Event ${eventType} not supported:`, e);
                    }
                });

                // Ensure focus
                messageBox.focus();

                // Try to trigger LinkedIn's internal change detection
                if (typeof messageBox.oninput === 'function') {
                    messageBox.oninput();
                }

                utils.log('Message populated successfully');

            }, 100);

        } catch (error) {
            utils.log('Error populating message box:', error);
            utils.showNotification('Error populating message box. Please paste manually.', 'error');

            // Fallback: copy to clipboard
            try {
                navigator.clipboard.writeText(message).then(() => {
                    utils.showNotification('Message copied to clipboard!', 'info');
                });
            } catch (clipboardError) {
                utils.log('Clipboard access failed:', clipboardError);
            }
        }
    }

    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((request, _sender, _sendResponse) => {
        if (request.action === 'companyPageLoaded') {
            utils.log('Received company page loaded message');
            if (utils.isLinkedInCompanyPage()) {
                extractCompanyOverview();
            }
        }
    });

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();