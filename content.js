// Content script for LinkedIn AI Message Helper
(function() {
    'use strict';

    // Wait for utils to be available
    if (!window.LinkedInAIUtils) {
        console.error('LinkedInAIUtils not available');
        return;
    }

    const utils = window.LinkedInAIUtils;
    let companyInfo = null;
    let isProcessing = false;

    // Initialize the extension
    function init() {
        utils.log('LinkedIn AI Message Helper initialized');

        if (utils.isLinkedInCompanyPage()) {
            utils.log('Company page detected, extracting overview...');
            extractCompanyOverview();
        }

        // Set up message box monitoring
        setupMessageBoxMonitoring();
    }

    // Extract company overview information
    async function extractCompanyOverview() {
        try {
            utils.showNotification('Extracting company information...', 'info', 2000);

            const companyName = utils.getCompanyNameFromPage();
            utils.log('Company name:', companyName);

            // Wait for the overview section to load
            const overviewSelectors = [
                '[data-test-id="about-us-section"]',
                '.org-about-us-organization-description',
                '.org-top-card-summary__description',
                '.org-about-module__description',
                '.break-words p',
                '.org-about-us-organization-description__text'
            ];

            let overviewText = '';

            try {
                const result = await utils.waitForAnyElement(overviewSelectors, 5000);
                overviewText = utils.cleanText(utils.extractTextContent(result.element));
                utils.log('Found overview using selector:', result.selector);
            } catch (error) {
                utils.log('No overview section found, trying alternative extraction');
                overviewText = extractAlternativeCompanyInfo();
            }

            // Also try to get additional company info
            const additionalInfo = extractAdditionalCompanyInfo();

            companyInfo = {
                name: companyName,
                overview: overviewText,
                additional: additionalInfo,
                url: window.location.href
            };

            const fullInfo = formatCompanyInfo(companyInfo);
            utils.log('Extracted company info:', fullInfo);

            if (overviewText || additionalInfo) {
                utils.showNotification('Company information extracted successfully!', 'success');
            } else {
                utils.showNotification('Limited company information found', 'warning');
            }

        } catch (error) {
            utils.log('Error extracting company overview:', error);
            utils.showNotification('Error extracting company information', 'error');
        }
    }

    // Extract alternative company information if overview not found
    function extractAlternativeCompanyInfo() {
        const selectors = [
            '.org-top-card-summary__description',
            '.org-top-card-summary__info-item',
            '.org-page-details__definition-text',
            '.org-top-card-summary__tagline'
        ];

        let info = '';
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = utils.cleanText(utils.extractTextContent(element));
                if (text && text.length > 10) {
                    info += text + '\n';
                }
            }
        }

        return info.trim();
    }

    // Extract additional company information
    function extractAdditionalCompanyInfo() {
        const info = [];

        // Industry
        const industrySelectors = [
            '.org-top-card-summary__industry',
            '[data-test-id="industry"]',
            '.org-page-details__definition-text',
            '.org-top-card-summary__info-item'
        ];

        for (const selector of industrySelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = utils.extractTextContent(element).trim();
                if (text && text.length > 2 && !text.includes('followers')) {
                    info.push(`Industry: ${text}`);
                    break;
                }
            }
        }

        // Company size
        const sizeSelectors = [
            '.org-top-card-summary__company-size',
            '[data-test-id="company-size"]',
            '.org-about-company-module__company-size'
        ];

        for (const selector of sizeSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = utils.extractTextContent(element).trim();
                if (text && (text.includes('employees') || text.includes('people'))) {
                    info.push(`Company Size: ${text}`);
                    break;
                }
            }
        }

        // Location/Headquarters
        const locationSelectors = [
            '.org-top-card-summary__headquarter',
            '[data-test-id="headquarters"]',
            '.org-about-company-module__headquarters'
        ];

        for (const selector of locationSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = utils.extractTextContent(element).trim();
                if (text && text.length > 2) {
                    info.push(`Headquarters: ${text}`);
                    break;
                }
            }
        }

        // Website
        const websiteElement = document.querySelector('a[data-test-id="organization-website"]') ||
                              document.querySelector('.org-about-company-module__website a');
        if (websiteElement) {
            const website = websiteElement.href || utils.extractTextContent(websiteElement);
            if (website && website.includes('http')) {
                info.push(`Website: ${website}`);
            }
        }

        // Followers count
        const followersElement = document.querySelector('.org-top-card-summary__follower-count') ||
                                document.querySelector('[data-test-id="follower-count"]');
        if (followersElement) {
            const followers = utils.extractTextContent(followersElement).trim();
            if (followers && followers.includes('followers')) {
                info.push(`LinkedIn Followers: ${followers}`);
            }
        }

        // Recent posts/updates (if visible)
        const recentPostElement = document.querySelector('.org-recent-update-card__text') ||
                                 document.querySelector('.feed-shared-text');
        if (recentPostElement) {
            const recentPost = utils.extractTextContent(recentPostElement).trim();
            if (recentPost && recentPost.length > 20 && recentPost.length < 200) {
                info.push(`Recent Update: ${recentPost.substring(0, 150)}...`);
            }
        }

        return info.join('\n');
    }

    // Format company information for AI processing
    function formatCompanyInfo(info) {
        let formatted = `Company: ${info.name}\n`;

        if (info.overview) {
            formatted += `\nOverview:\n${info.overview}\n`;
        }

        if (info.additional) {
            formatted += `\nAdditional Information:\n${info.additional}\n`;
        }

        return formatted.trim();
    }

    // Set up monitoring for message compose boxes
    function setupMessageBoxMonitoring() {
        // Monitor for message button clicks and compose box appearances
        const debouncedMonitor = utils.debounce(monitorForMessageBox, 300);

        // Watch for DOM changes that might indicate message popup opening
        const observer = new MutationObserver(debouncedMonitor);
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Also monitor for "Message" button clicks specifically
        setupMessageButtonMonitoring();

        // Check immediately
        monitorForMessageBox();
    }

    // Monitor for "Message" button clicks on company pages
    function setupMessageButtonMonitoring() {
        // Selectors for the "Message" button on company pages
        const messageButtonSelectors = [
            '.org-top-card-primary-actions__action button[aria-label*="Message"]',
            '.org-top-card-primary-actions__action button[data-control-name*="message"]',
            'button[aria-label*="Send message"]',
            'button[data-control-name="message"]',
            '.pv-s-profile-actions button[aria-label*="Message"]'
        ];

        // Add click listeners to message buttons
        messageButtonSelectors.forEach(selector => {
            document.addEventListener('click', function(event) {
                if (event.target.matches(selector) || event.target.closest(selector)) {
                    utils.log('Message button clicked, waiting for popup...');
                    // Wait a bit for the popup to appear, then check for message box
                    setTimeout(() => {
                        monitorForMessageBox();
                    }, 500);
                    setTimeout(() => {
                        monitorForMessageBox();
                    }, 1000);
                    setTimeout(() => {
                        monitorForMessageBox();
                    }, 1500);
                }
            }, true);
        });
    }

    // Monitor for message compose boxes (popup that appears when clicking "Message" button)
    function monitorForMessageBox() {
        if (isProcessing) return;

        // Selectors for LinkedIn message popup compose boxes
        const messageBoxSelectors = [
            // Textarea selectors (most common for company page message popup)
            'textarea[placeholder*="Write a message"]',
            'textarea.artdeco-text-input--input[placeholder*="Write a message"]',
            'textarea#org-message-page-modal-message',
            'textarea[name="message"]',
            // Contenteditable selectors (fallback)
            '.msg-form__contenteditable[placeholder*="Write a message"]',
            '.msg-form__contenteditable[data-placeholder*="Write a message"]',
            '.msg-form__msg-content-container .msg-form__contenteditable',
            '.msg-form__contenteditable',
            '.compose-form__message-texteditor',
            '.artdeco-text-input--input[placeholder*="Write a message"]',
            '[data-test-id="compose-message-field"]',
            // Additional fallback selectors
            '.msg-form__message-texteditor',
            '.compose-form__message-field',
            'div[contenteditable="true"][data-placeholder*="message"]',
            'div[contenteditable="true"][placeholder*="message"]'
        ];

        for (const selector of messageBoxSelectors) {
            const messageBox = document.querySelector(selector);
            if (messageBox && !messageBox.hasAttribute('data-ai-helper-processed')) {
                // Check if the message box is visible (popup is open)
                const isVisible = messageBox.offsetParent !== null &&
                                getComputedStyle(messageBox).display !== 'none' &&
                                getComputedStyle(messageBox).visibility !== 'hidden';

                if (isVisible) {
                    utils.log('Message compose box detected:', selector);
                    handleMessageBoxDetected(messageBox);
                    break;
                }
            }
        }
    }

    // Handle when message box is detected
    async function handleMessageBoxDetected(messageBox) {
        if (!companyInfo) {
            utils.showNotification('No company information available. Please visit a company page first.', 'warning');
            return;
        }

        try {
            isProcessing = true;
            messageBox.setAttribute('data-ai-helper-processed', 'true');

            utils.showNotification('Generating personalized message...', 'info');

            // Request message generation from background script
            const response = await chrome.runtime.sendMessage({
                action: 'generateMessage',
                companyInfo: formatCompanyInfo(companyInfo),
                companyName: companyInfo.name
            });

            if (response.success) {
                populateMessageBox(messageBox, response.message);
                utils.showNotification('Message generated successfully!', 'success');
            } else {
                utils.showNotification(`Error: ${response.error}`, 'error');
            }

        } catch (error) {
            utils.log('Error generating message:', error);
            utils.showNotification('Error generating message', 'error');
        } finally {
            isProcessing = false;
        }
    }

    // Populate the message box with generated content
    function populateMessageBox(messageBox, message) {
        try {
            utils.log('Attempting to populate message box with:', message);

            // Add a small delay to ensure the message box is ready
            setTimeout(() => {
                // Focus the message box first
                messageBox.focus();

                utils.log('Message box element type:', messageBox.tagName);
                utils.log('Message box classes:', messageBox.className);
                utils.log('Message box id:', messageBox.id);

                // Handle different types of message boxes
                if (messageBox.tagName === 'TEXTAREA' || messageBox.tagName === 'INPUT') {
                    // For textarea/input elements (most common for company page popups)
                    utils.log('Populating textarea element');
                    messageBox.value = message;

                    // Clear any existing content first
                    messageBox.value = '';
                    // Set the new message
                    messageBox.value = message;

                } else if (messageBox.contentEditable === 'true') {
                    // For contenteditable elements
                    utils.log('Populating contenteditable element');
                    messageBox.innerHTML = '';

                    // Method 1: Use innerHTML with proper formatting
                    const formattedMessage = message.replace(/\n/g, '<br>');
                    messageBox.innerHTML = formattedMessage;

                    // Method 2: If innerHTML doesn't work, try textContent
                    if (!messageBox.innerHTML || messageBox.innerHTML === '') {
                        messageBox.textContent = message;
                    }

                    // Method 3: If still empty, try inserting text nodes
                    if (!messageBox.textContent || messageBox.textContent === '') {
                        const lines = message.split('\n');
                        lines.forEach((line, index) => {
                            if (index > 0) {
                                messageBox.appendChild(document.createElement('br'));
                            }
                            const textNode = document.createTextNode(line);
                            messageBox.appendChild(textNode);
                        });
                    }

                } else {
                    // Fallback method
                    utils.log('Using fallback method');
                    messageBox.textContent = message;
                }

                // Trigger comprehensive events to notify LinkedIn's JavaScript
                const events = [
                    'input', 'change', 'keyup', 'keydown', 'keypress',
                    'focus', 'blur', 'paste', 'textInput'
                ];

                events.forEach(eventType => {
                    try {
                        let event;
                        if (eventType === 'input') {
                            event = new InputEvent(eventType, {
                                bubbles: true,
                                cancelable: true,
                                inputType: 'insertText',
                                data: message
                            });
                        } else if (eventType === 'change') {
                            event = new Event(eventType, { bubbles: true, cancelable: true });
                        } else {
                            event = new Event(eventType, { bubbles: true, cancelable: true });
                        }
                        messageBox.dispatchEvent(event);
                        utils.log(`Triggered ${eventType} event`);
                    } catch (e) {
                        // Some events might not be supported
                        utils.log(`Event ${eventType} not supported:`, e);
                    }
                });

                // Special handling for textarea elements
                if (messageBox.tagName === 'TEXTAREA') {
                    // Simulate typing for better LinkedIn integration
                    const inputEvent = new Event('input', { bubbles: true });
                    const changeEvent = new Event('change', { bubbles: true });

                    messageBox.dispatchEvent(inputEvent);
                    messageBox.dispatchEvent(changeEvent);

                    // Trigger any form validation
                    if (messageBox.form) {
                        const formEvent = new Event('input', { bubbles: true });
                        messageBox.form.dispatchEvent(formEvent);
                    }
                }

                // Additional LinkedIn-specific triggers
                try {
                    // Trigger any attached event handlers
                    if (messageBox.oninput) messageBox.oninput();
                    if (messageBox.onchange) messageBox.onchange();
                    if (messageBox.onkeyup) messageBox.onkeyup();

                    // Try to trigger React/Vue change detection
                    const reactKey = Object.keys(messageBox).find(key => key.startsWith('__react'));
                    if (reactKey) {
                        const reactInstance = messageBox[reactKey];
                        if (reactInstance && reactInstance.memoizedProps && reactInstance.memoizedProps.onChange) {
                            reactInstance.memoizedProps.onChange({ target: messageBox });
                        }
                    }
                } catch (e) {
                    utils.log('React/Vue trigger failed:', e);
                }

                // Ensure focus and cursor position
                messageBox.focus();

                // Set cursor to end of text
                if (messageBox.contentEditable === 'true') {
                    const range = document.createRange();
                    const selection = window.getSelection();
                    range.selectNodeContents(messageBox);
                    range.collapse(false);
                    selection.removeAllRanges();
                    selection.addRange(range);
                } else if (messageBox.tagName === 'TEXTAREA') {
                    // Set cursor to end for textarea
                    messageBox.setSelectionRange(messageBox.value.length, messageBox.value.length);
                }

                // Final verification
                const finalValue = messageBox.value || messageBox.textContent || messageBox.innerHTML;
                utils.log('Message populated successfully. Final content:', finalValue);

                if (!finalValue || finalValue.trim() === '') {
                    utils.log('Warning: Message box appears to be empty after population');
                    utils.showNotification('Message may not have populated correctly. Check the message box.', 'warning');
                }

            }, 200); // Slightly longer delay for popup to fully load

        } catch (error) {
            utils.log('Error populating message box:', error);
            utils.showNotification('Error populating message box. Message copied to clipboard.', 'warning');

            // Fallback: copy to clipboard
            try {
                navigator.clipboard.writeText(message).then(() => {
                    utils.showNotification('Message copied to clipboard! Please paste manually.', 'info', 5000);
                });
            } catch (clipboardError) {
                utils.log('Clipboard access failed:', clipboardError);
                utils.showNotification('Please copy this message manually: ' + message, 'error', 10000);
            }
        }
    }

    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((request, _sender, _sendResponse) => {
        if (request.action === 'companyPageLoaded') {
            utils.log('Received company page loaded message');
            if (utils.isLinkedInCompanyPage()) {
                extractCompanyOverview();
            }
        }
    });

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();