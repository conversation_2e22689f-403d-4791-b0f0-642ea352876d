<!DOCTYPE html>
<html>
<head>
    <title>LinkedIn AI Message Helper - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-button {
            background: #0a66c2;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #004182;
        }
        .message-box {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <h1>LinkedIn AI Message Helper - Test Page</h1>
    
    <div class="section">
        <h2>Extension Installation Test</h2>
        <p>This page helps you test the LinkedIn AI Message Helper extension.</p>
        <div id="extensionStatus" class="status">Checking extension status...</div>
    </div>
    
    <div class="section">
        <h2>Quick Start Guide</h2>
        <ol>
            <li><strong>Install the Extension</strong>:
                <ul>
                    <li>Open Chrome and go to <code>chrome://extensions/</code></li>
                    <li>Enable "Developer mode" (top right toggle)</li>
                    <li>Click "Load unpacked" and select this folder</li>
                </ul>
            </li>
            <li><strong>Configure the Extension</strong>:
                <ul>
                    <li>Click the extension icon in your toolbar</li>
                    <li>Set your system prompt or use the default</li>
                    <li>Optionally add your OpenAI API key</li>
                    <li>Click "Save Prompt"</li>
                </ul>
            </li>
            <li><strong>Test on LinkedIn</strong>:
                <ul>
                    <li>Visit a LinkedIn company page (e.g., <a href="https://www.linkedin.com/company/microsoft/" target="_blank">Microsoft</a>)</li>
                    <li>Wait for the "Company information extracted" notification</li>
                    <li>Click "Message" on the company page</li>
                    <li>The message box should auto-populate with a personalized message</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <div class="section">
        <h2>Test Company Pages</h2>
        <p>Try these LinkedIn company pages to test the extension:</p>
        <button class="test-button" onclick="window.open('https://www.linkedin.com/company/microsoft/', '_blank')">Microsoft</button>
        <button class="test-button" onclick="window.open('https://www.linkedin.com/company/google/', '_blank')">Google</button>
        <button class="test-button" onclick="window.open('https://www.linkedin.com/company/apple/', '_blank')">Apple</button>
        <button class="test-button" onclick="window.open('https://www.linkedin.com/company/amazon/', '_blank')">Amazon</button>
        <button class="test-button" onclick="window.open('https://www.linkedin.com/company/tesla/', '_blank')">Tesla</button>
    </div>
    
    <div class="section">
        <h2>Troubleshooting</h2>
        <h3>Common Issues:</h3>
        <ul>
            <li><strong>Extension not working</strong>: Make sure it's enabled in chrome://extensions/</li>
            <li><strong>No company info extracted</strong>: Refresh the LinkedIn page and wait a few seconds</li>
            <li><strong>Message not populating</strong>: Try clicking in the message box first</li>
            <li><strong>API errors</strong>: Check your OpenAI API key or use demo mode</li>
        </ul>
        
        <h3>Check Browser Console:</h3>
        <p>Press F12 and look for messages starting with "[LinkedIn AI Helper]"</p>
    </div>
    
    <div class="section">
        <h2>Demo Message Box</h2>
        <p>This simulates a LinkedIn message compose box (for testing purposes only):</p>
        <textarea class="message-box" placeholder="Type your message here..."></textarea>
        <br>
        <button class="test-button" onclick="testMessageGeneration()">Test Message Generation</button>
    </div>
    
    <script>
        // Check if extension is loaded
        function checkExtensionStatus() {
            const statusDiv = document.getElementById('extensionStatus');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                statusDiv.innerHTML = '✅ Chrome extension API available';
                statusDiv.style.background = '#e8f5e8';
                statusDiv.style.borderColor = '#4caf50';
                statusDiv.style.color = '#2e7d32';
            } else {
                statusDiv.innerHTML = '❌ Chrome extension API not available - make sure you\'re testing in Chrome';
                statusDiv.style.background = '#ffebee';
                statusDiv.style.borderColor = '#f44336';
                statusDiv.style.color = '#c62828';
            }
        }
        
        function testMessageGeneration() {
            alert('This is just a demo. To test the actual extension, visit a LinkedIn company page and click the Message button.');
        }
        
        // Check status when page loads
        checkExtensionStatus();
    </script>
</body>
</html>
