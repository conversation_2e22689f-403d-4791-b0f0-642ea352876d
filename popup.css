* {
    box-sizing: border-box;
}

body {
    width: 450px;
    min-height: 600px;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    padding: 0;
    background: white;
    min-height: 100vh;
}

/* Header */
.header {
    background: linear-gradient(135deg, #0a66c2 0%, #004182 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.subtitle {
    margin: 5px 0 0 0;
    font-size: 12px;
    opacity: 0.9;
}

/* Sections */
.section {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
    border-bottom: none;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.required {
    background: #ff4757;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
}

.optional {
    background: #5352ed;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
}

/* Input Groups */
.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 13px;
    color: #555;
}

.input-with-button {
    display: flex;
    gap: 5px;
}

.input-with-button input {
    flex: 1;
}

.toggle-btn {
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.toggle-btn:hover {
    background: #e9ecef;
}

input[type="password"], input[type="text"], textarea, select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 13px;
    font-family: inherit;
    transition: border-color 0.2s;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #0a66c2;
}

textarea {
    resize: vertical;
    min-height: 80px;
}

.help-text {
    display: block;
    margin-top: 5px;
    font-size: 11px;
    color: #666;
}

.help-text a {
    color: #0a66c2;
    text-decoration: none;
}

.help-text a:hover {
    text-decoration: underline;
}

/* Buttons */
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.primary-btn, .secondary-btn {
    flex: 1;
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.primary-btn {
    background: #0a66c2;
    color: white;
}

.primary-btn:hover {
    background: #004182;
    transform: translateY(-1px);
}

.secondary-btn {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.secondary-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

/* Status Display */
.status-display {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    font-weight: 500;
    color: #555;
    font-size: 12px;
}

.status-value {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    background: #e9ecef;
    color: #495057;
}

.status-value.success {
    background: #d4edda;
    color: #155724;
}

.status-value.error {
    background: #f8d7da;
    color: #721c24;
}

.status-value.warning {
    background: #fff3cd;
    color: #856404;
}

/* Instructions */
.instructions-list {
    margin: 0;
    padding-left: 20px;
    font-size: 13px;
    line-height: 1.5;
}

.instructions-list li {
    margin-bottom: 10px;
}

.instructions-list strong {
    color: #0a66c2;
}

/* Templates */
.prompt-templates {
    margin-bottom: 15px;
}

.prompt-templates label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 13px;
    color: #555;
}

/* Footer */
.footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.footer-links {
    margin-bottom: 10px;
}

.footer-links a {
    color: #0a66c2;
    text-decoration: none;
    font-size: 12px;
    margin: 0 10px;
}

.footer-links a:hover {
    text-decoration: underline;
}

.version {
    margin: 0;
    font-size: 11px;
    color: #666;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.section {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    body {
        width: 350px;
    }

    .button-group {
        flex-direction: column;
    }
}