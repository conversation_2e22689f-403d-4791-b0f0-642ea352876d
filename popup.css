body {
    width: 400px;
    min-height: 500px;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
}

.container {
    padding: 20px;
}

h1 {
    color: #0a66c2;
    font-size: 18px;
    margin: 0 0 20px 0;
    text-align: center;
    border-bottom: 2px solid #0a66c2;
    padding-bottom: 10px;
}

h3 {
    color: #333;
    font-size: 14px;
    margin: 0 0 10px 0;
}

.section {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    font-family: inherit;
    resize: vertical;
    box-sizing: border-box;
}

input[type="password"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 10px;
    box-sizing: border-box;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

button {
    flex: 1;
    padding: 8px 12px;
    background-color: #0a66c2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #004182;
}

button:active {
    background-color: #003366;
}

#saveApiKey {
    width: 100%;
}

.status-info {
    padding: 8px;
    background-color: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 4px;
    color: #2e7d32;
    font-size: 12px;
}

.status-error {
    padding: 8px;
    background-color: #ffebee;
    border: 1px solid #f44336;
    border-radius: 4px;
    color: #c62828;
    font-size: 12px;
}

.status-warning {
    padding: 8px;
    background-color: #fff3e0;
    border: 1px solid #ff9800;
    border-radius: 4px;
    color: #ef6c00;
    font-size: 12px;
}

ol {
    margin: 0;
    padding-left: 20px;
    font-size: 12px;
    color: #666;
}

ol li {
    margin-bottom: 5px;
}