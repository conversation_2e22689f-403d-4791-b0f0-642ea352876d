<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="popup.css">
    <title>LinkedIn AI Message Helper</title>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🤖 LinkedIn AI Helper</h1>
            <p class="subtitle">Generate personalized messages with AI</p>
        </header>

        <!-- API Key Configuration -->
        <div class="section api-section">
            <div class="section-header">
                <h3>🔑 OpenAI API Configuration</h3>
                <span class="required">Required for AI generation</span>
            </div>

            <div class="input-group">
                <label for="apiKey">API Key:</label>
                <div class="input-with-button">
                    <input type="password" id="apiKey" placeholder="sk-..." autocomplete="off">
                    <button id="toggleApiKey" class="toggle-btn" title="Show/Hide API Key">👁️</button>
                </div>
                <small class="help-text">
                    Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a>
                </small>
            </div>

            <div class="button-group">
                <button id="saveApiKey" class="primary-btn">💾 Save API Key</button>
                <button id="testApiKey" class="secondary-btn">🧪 Test Connection</button>
            </div>
        </div>

        <!-- System Prompt Configuration -->
        <div class="section prompt-section">
            <div class="section-header">
                <h3>📝 System Prompt</h3>
                <span class="optional">Customize AI behavior</span>
            </div>

            <div class="prompt-templates">
                <label for="promptTemplate">Quick Templates:</label>
                <select id="promptTemplate">
                    <option value="">Choose a template...</option>
                    <option value="sales">Sales Outreach</option>
                    <option value="recruiting">Recruiting</option>
                    <option value="partnership">Partnership</option>
                    <option value="networking">Networking</option>
                    <option value="custom">Custom</option>
                </select>
            </div>

            <div class="input-group">
                <label for="systemPrompt">System Prompt:</label>
                <textarea id="systemPrompt" rows="6" placeholder="Enter your custom system prompt here..."></textarea>
                <small class="help-text">
                    This tells the AI how to write messages. Be specific about tone, length, and style.
                </small>
            </div>

            <div class="button-group">
                <button id="savePrompt" class="primary-btn">💾 Save Prompt</button>
                <button id="resetPrompt" class="secondary-btn">🔄 Reset to Default</button>
            </div>
        </div>

        <!-- Status Section -->
        <div class="section status-section">
            <div class="section-header">
                <h3>📊 Status</h3>
            </div>
            <div id="status" class="status-display">
                <div class="status-item">
                    <span class="status-label">API Key:</span>
                    <span id="apiStatus" class="status-value">Not configured</span>
                </div>
                <div class="status-item">
                    <span class="status-label">System Prompt:</span>
                    <span id="promptStatus" class="status-value">Default</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Mode:</span>
                    <span id="modeStatus" class="status-value">Template Mode</span>
                </div>
            </div>
        </div>

        <!-- Usage Instructions -->
        <div class="section instructions-section">
            <div class="section-header">
                <h3>📋 How to Use</h3>
            </div>
            <ol class="instructions-list">
                <li>
                    <strong>Configure API Key:</strong> Add your OpenAI API key above
                </li>
                <li>
                    <strong>Customize Prompt:</strong> Choose a template or write your own
                </li>
                <li>
                    <strong>Visit Company Page:</strong> Go to any LinkedIn company page
                </li>
                <li>
                    <strong>Click Message:</strong> Click the "Message" button on the page
                </li>
                <li>
                    <strong>Auto-Generated:</strong> Your personalized message appears!
                </li>
            </ol>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-links">
                <a href="#" id="viewLogs">View Logs</a>
                <a href="#" id="clearData">Clear Data</a>
                <a href="https://github.com/your-repo" target="_blank">Help</a>
            </div>
            <p class="version">v1.4 - Made with ❤️</p>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>