<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <h1>LinkedIn AI Message Helper</h1>

        <div class="section">
            <h3>System Prompt Configuration</h3>
            <textarea id="systemPrompt" placeholder="Enter your personalized system prompt here. This will be used to generate messages based on company information.

Example: 'You are a professional sales representative. Generate a personalized outreach message based on the company information provided. Keep it professional, concise, and engaging. Focus on how our services can benefit their business.'"></textarea>

            <div class="button-group">
                <button id="savePrompt">Save Prompt</button>
                <button id="resetPrompt">Reset to Default</button>
            </div>
        </div>

        <div class="section">
            <h3>OpenAI API Configuration</h3>
            <input type="password" id="apiKey" placeholder="Enter your OpenAI API key (optional for demo)">
            <button id="saveApiKey">Save API Key</button>
        </div>

        <div class="section">
            <h3>Status</h3>
            <div id="status" class="status-info">Ready to generate messages</div>
        </div>

        <div class="section">
            <h3>Instructions</h3>
            <ol>
                <li>Set your system prompt above</li>
                <li>Visit a LinkedIn company page</li>
                <li>Click "Message" on the company page</li>
                <li>The extension will auto-populate your message</li>
            </ol>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>