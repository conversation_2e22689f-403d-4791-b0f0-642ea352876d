document.addEventListener('DOMContentLoaded', function() {
    const systemPromptTextarea = document.getElementById('systemPrompt');
    const apiKeyInput = document.getElementById('apiKey');
    const savePromptBtn = document.getElementById('savePrompt');
    const resetPromptBtn = document.getElementById('resetPrompt');
    const saveApiKeyBtn = document.getElementById('saveApiKey');
    const statusDiv = document.getElementById('status');

    const defaultPrompt = `You are a professional sales representative. Generate a personalized outreach message based on the company information provided. Keep it professional, concise, and engaging. Focus on how our services can benefit their business. The message should be:
- Professional and respectful
- Personalized based on company details
- Brief (2-3 sentences)
- Include a clear call to action
- Avoid being overly salesy`;

    // Load saved data
    loadSavedData();

    // Event listeners
    savePromptBtn.addEventListener('click', saveSystemPrompt);
    resetPromptBtn.addEventListener('click', resetToDefault);
    saveApiKeyBtn.addEventListener('click', saveApiKey);

    function loadSavedData() {
        chrome.storage.sync.get(['systemPrompt', 'openaiApiKey'], function(result) {
            if (result.systemPrompt) {
                systemPromptTextarea.value = result.systemPrompt;
            } else {
                systemPromptTextarea.value = defaultPrompt;
            }

            if (result.openaiApiKey) {
                apiKeyInput.value = result.openaiApiKey;
                updateStatus('Configuration loaded successfully', 'info');
            } else {
                updateStatus('No API key saved - using demo mode', 'warning');
            }
        });
    }

    function saveSystemPrompt() {
        const prompt = systemPromptTextarea.value.trim();
        if (!prompt) {
            updateStatus('Please enter a system prompt', 'error');
            return;
        }

        chrome.storage.sync.set({systemPrompt: prompt}, function() {
            updateStatus('System prompt saved successfully', 'info');
        });
    }

    function resetToDefault() {
        systemPromptTextarea.value = defaultPrompt;
        chrome.storage.sync.set({systemPrompt: defaultPrompt}, function() {
            updateStatus('Reset to default prompt', 'info');
        });
    }

    function saveApiKey() {
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            chrome.storage.sync.remove('openaiApiKey', function() {
                updateStatus('API key removed - using demo mode', 'warning');
            });
            return;
        }

        chrome.storage.sync.set({openaiApiKey: apiKey}, function() {
            updateStatus('API key saved successfully', 'info');
        });
    }

    function updateStatus(message, type) {
        statusDiv.textContent = message;
        statusDiv.className = `status-${type}`;

        // Clear status after 3 seconds for non-error messages
        if (type !== 'error') {
            setTimeout(() => {
                statusDiv.textContent = 'Ready to generate messages';
                statusDiv.className = 'status-info';
            }, 3000);
        }
    }
});