document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const systemPromptTextarea = document.getElementById('systemPrompt');
    const apiKeyInput = document.getElementById('apiKey');
    const promptTemplateSelect = document.getElementById('promptTemplate');
    const toggleApiKeyBtn = document.getElementById('toggleApiKey');
    const savePromptBtn = document.getElementById('savePrompt');
    const resetPromptBtn = document.getElementById('resetPrompt');
    const saveApiKeyBtn = document.getElementById('saveApiKey');
    const testApiKeyBtn = document.getElementById('testApiKey');
    const viewLogsBtn = document.getElementById('viewLogs');
    const clearDataBtn = document.getElementById('clearData');

    // Status elements
    const apiStatus = document.getElementById('apiStatus');
    const promptStatus = document.getElementById('promptStatus');
    const modeStatus = document.getElementById('modeStatus');

    // Prompt templates
    const promptTemplates = {
        sales: `You are a professional sales representative. Generate a personalized LinkedIn outreach message based on the company information provided. The message should be:
- Professional and respectful
- Personalized based on company details
- Brief (2-3 sentences maximum)
- Include a soft call to action
- Focus on value proposition
- Avoid being overly salesy`,

        recruiting: `You are a professional recruiter. Generate a personalized LinkedIn outreach message based on the company information provided. The message should be:
- Professional and engaging
- Show genuine interest in the company
- Brief (2-3 sentences maximum)
- Mention relevant opportunities
- Invite for a conversation
- Sound authentic and human`,

        partnership: `You are a business development professional. Generate a personalized LinkedIn outreach message based on the company information provided. The message should be:
- Professional and collaborative
- Highlight potential synergies
- Brief (2-3 sentences maximum)
- Suggest mutual benefits
- Propose a discussion
- Sound strategic and thoughtful`,

        networking: `You are a professional looking to expand your network. Generate a personalized LinkedIn outreach message based on the company information provided. The message should be:
- Friendly and professional
- Show genuine interest in their work
- Brief (2-3 sentences maximum)
- Suggest connecting for mutual learning
- Be authentic and personal
- Avoid any sales pitch`
    };

    const defaultPrompt = promptTemplates.sales;

    // Initialize
    loadSavedData();
    setupEventListeners();

    function setupEventListeners() {
        // Template selection
        promptTemplateSelect.addEventListener('change', handleTemplateChange);

        // API key toggle
        toggleApiKeyBtn.addEventListener('click', toggleApiKeyVisibility);

        // Save buttons
        savePromptBtn.addEventListener('click', saveSystemPrompt);
        resetPromptBtn.addEventListener('click', resetToDefault);
        saveApiKeyBtn.addEventListener('click', saveApiKey);
        testApiKeyBtn.addEventListener('click', testApiKey);

        // Footer actions
        viewLogsBtn.addEventListener('click', viewLogs);
        clearDataBtn.addEventListener('click', clearAllData);
    }

    function loadSavedData() {
        chrome.storage.sync.get(['systemPrompt', 'openaiApiKey'], function(result) {
            // Load system prompt
            if (result.systemPrompt) {
                systemPromptTextarea.value = result.systemPrompt;

                // Try to detect which template is being used
                const template = detectTemplate(result.systemPrompt);
                if (template) {
                    promptTemplateSelect.value = template;
                }
            } else {
                systemPromptTextarea.value = defaultPrompt;
                promptTemplateSelect.value = 'sales';
            }

            // Load API key
            if (result.openaiApiKey) {
                apiKeyInput.value = result.openaiApiKey;
            }

            updateStatusDisplay();
        });
    }

    function handleTemplateChange() {
        const selectedTemplate = promptTemplateSelect.value;
        if (selectedTemplate && promptTemplates[selectedTemplate]) {
            systemPromptTextarea.value = promptTemplates[selectedTemplate];
        } else if (selectedTemplate === 'custom') {
            systemPromptTextarea.value = '';
            systemPromptTextarea.focus();
        }
    }

    function toggleApiKeyVisibility() {
        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            toggleApiKeyBtn.textContent = '🙈';
        } else {
            apiKeyInput.type = 'password';
            toggleApiKeyBtn.textContent = '👁️';
        }
    }

    function saveSystemPrompt() {
        const prompt = systemPromptTextarea.value.trim();
        if (!prompt) {
            showNotification('Please enter a system prompt', 'error');
            return;
        }

        chrome.storage.sync.set({systemPrompt: prompt}, function() {
            showNotification('System prompt saved successfully!', 'success');
            updateStatusDisplay();
        });
    }

    function resetToDefault() {
        systemPromptTextarea.value = defaultPrompt;
        promptTemplateSelect.value = 'sales';
        chrome.storage.sync.set({systemPrompt: defaultPrompt}, function() {
            showNotification('Reset to default prompt', 'success');
            updateStatusDisplay();
        });
    }

    function saveApiKey() {
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            chrome.storage.sync.remove('openaiApiKey', function() {
                showNotification('API key removed', 'warning');
                updateStatusDisplay();
            });
            return;
        }

        if (!apiKey.startsWith('sk-')) {
            showNotification('Invalid API key format. Should start with "sk-"', 'error');
            return;
        }

        chrome.storage.sync.set({openaiApiKey: apiKey}, function() {
            showNotification('API key saved successfully!', 'success');
            updateStatusDisplay();
        });
    }

    async function testApiKey() {
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            showNotification('Please enter an API key first', 'error');
            return;
        }

        if (!apiKey.startsWith('sk-')) {
            showNotification('Invalid API key format', 'error');
            return;
        }

        testApiKeyBtn.textContent = '🔄 Testing...';
        testApiKeyBtn.disabled = true;

        try {
            const response = await fetch('https://api.openai.com/v1/models', {
                headers: {
                    'Authorization': `Bearer ${apiKey}`
                }
            });

            if (response.ok) {
                showNotification('✅ API key is valid!', 'success');
            } else {
                showNotification('❌ API key is invalid', 'error');
            }
        } catch (error) {
            showNotification('❌ Failed to test API key', 'error');
        } finally {
            testApiKeyBtn.textContent = '🧪 Test Connection';
            testApiKeyBtn.disabled = false;
        }
    }

    function updateStatusDisplay() {
        chrome.storage.sync.get(['systemPrompt', 'openaiApiKey'], function(result) {
            // Update API status
            if (result.openaiApiKey) {
                apiStatus.textContent = 'Configured';
                apiStatus.className = 'status-value success';
                modeStatus.textContent = 'AI Mode';
                modeStatus.className = 'status-value success';
            } else {
                apiStatus.textContent = 'Not configured';
                apiStatus.className = 'status-value error';
                modeStatus.textContent = 'Template Mode';
                modeStatus.className = 'status-value warning';
            }

            // Update prompt status
            if (result.systemPrompt) {
                const template = detectTemplate(result.systemPrompt);
                promptStatus.textContent = template ? template.charAt(0).toUpperCase() + template.slice(1) : 'Custom';
                promptStatus.className = 'status-value success';
            } else {
                promptStatus.textContent = 'Default';
                promptStatus.className = 'status-value';
            }
        });
    }

    function detectTemplate(prompt) {
        for (const [key, template] of Object.entries(promptTemplates)) {
            if (prompt.includes(template.substring(0, 50))) {
                return key;
            }
        }
        return null;
    }

    function viewLogs() {
        chrome.tabs.create({
            url: 'chrome://extensions/?id=' + chrome.runtime.id
        });
    }

    function clearAllData() {
        if (confirm('Are you sure you want to clear all saved data? This cannot be undone.')) {
            chrome.storage.sync.clear(function() {
                systemPromptTextarea.value = defaultPrompt;
                apiKeyInput.value = '';
                promptTemplateSelect.value = 'sales';
                showNotification('All data cleared', 'warning');
                updateStatusDisplay();
            });
        }
    }

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '10px',
            right: '10px',
            padding: '10px 15px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            fontSize: '12px',
            zIndex: '1000',
            maxWidth: '300px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            transition: 'all 0.3s ease'
        });

        // Type-specific colors
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };

        notification.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(notification);

        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 3000);
    }
});